# 🌟 HVAC Offer Generator - Inteligentny Generator Ofert

## 📋 Opis

Zaawansowany system generowania ofert HVAC wykorzystujący **LangChain Go** i **analizę semantyczną** do tworzenia spersonalizowanych ofert PDF na podstawie historii komunikacji z klientem.

## 🚀 Kluczowe Funkcje

### 🧠 **Analiza Semantyczna <PERSON>lientów**
- **Automatyczna analiza emaili** - wyciąganie wymagań, budżetu, preferencji
- **Rozpoznawanie tonu komunikacji** - formalna vs nieformalna
- **Identyfikacja terminów technicznych** - inverter, WiFi, cicha praca
- **Wykrywanie sentymentu** - pozytywny, neutralny, negatywny

### 🏠 **Baza Urządzeń LG**
- **LG Standard S12ET** - podstawowy model split (3.5kW, A++, 19dB)
- **LG Dual Cool S18ET** - premium z WiFi (5.0kW, A+++, 17dB)
- **LG Artcool Gallery A12FR** - designerski model (3.5kW, A+++, 16dB)

### 📄 **Generator PDF**
- **Piękne oferty PDF** z logo, zdjęciami urządzeń
- **Spersonalizowane wprowadzenie** dopasowane do klienta
- **Szczegółowe specyfikacje** techniczne i cenowe
- **Komponenty instalacji** z cenami i opisami

### 🎯 **Inteligentne Dopasowanie**
- **Algorytm scoringu** - rozmiar pomieszczenia, budżet, funkcje
- **3 rekomendacje** najlepiej dopasowanych urządzeń
- **Powody wyboru** - dlaczego to urządzenie pasuje
- **Alternatywne opcje** - różne poziomy cenowe

## 🛠️ Instalacja i Uruchomienie

### 1. **Wymagania**
```bash
# Go 1.21+
go version

# Wymagane pakiety
go mod tidy
```

### 2. **Konfiguracja**
```bash
# Ustaw klucz OpenAI API
export OPENAI_API_KEY="your-openai-api-key"

# Opcjonalnie - WatsonX.ai
export WATSONX_API_KEY="your-watsonx-api-key"
export WATSONX_PROJECT_ID="your-project-id"
```

### 3. **Uruchomienie Serwisu**
```bash
# Uruchom serwis ofert
cd cmd/offer-service
go run main.go

# Serwis dostępny na porcie 8081
# http://localhost:8081
```

## 📡 API Endpoints

### **POST /api/offers/generate**
Generuje nową ofertę dla klienta
```json
{
  "customer_id": "customer_123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Offer generated successfully",
  "offer": {
    "offer_id": "HVAC-**********",
    "customer_profile": {
      "name": "Jan Kowalski",
      "email": "<EMAIL>",
      "communication_tone": "formal",
      "requirements": {
        "room_size": 65.0,
        "room_count": 2,
        "special_features": ["inverter", "wifi"]
      }
    },
    "recommendations": [
      {
        "equipment": {
          "model_code": "S18ET_DUAL",
          "name": "LG Dual Cool S18ET",
          "capacity": 5.0,
          "energy_class": "A+++",
          "pricing": {
            "total_price": 8700.0
          }
        },
        "match_score": 0.95,
        "match_reasons": [
          "Idealnie dopasowany do pomieszczenia 65m²",
          "Posiada funkcję: WiFi",
          "Najwyższa klasa energetyczna A+++"
        ]
      }
    ],
    "summary": {
      "final_price": 8700.0,
      "total_discount": 500.0
    }
  },
  "pdf_url": "/api/offers/HVAC-**********/pdf"
}
```

### **GET /api/offers/{offer_id}/pdf**
Pobiera PDF oferty
```bash
curl -O http://localhost:8081/api/offers/HVAC-**********/pdf
```

### **GET /api/offers/test**
Generuje testową ofertę
```bash
curl http://localhost:8081/api/offers/test
```

### **GET /health**
Sprawdza status serwisu
```json
{
  "success": true,
  "message": "Offer service is healthy",
  "timestamp": **********,
  "version": "1.0.0"
}
```

## 🧪 Testowanie

### **Uruchom testy**
```bash
# Wszystkie testy
go test ./internal/offer/...

# Testy z verbose
go test -v ./internal/offer/...

# Benchmark
go test -bench=. ./internal/offer/...

# Test coverage
go test -cover ./internal/offer/...
```

### **Test manualny**
```bash
# Wygeneruj testową ofertę
curl -X GET http://localhost:8081/api/offers/test

# Pobierz PDF
curl -O http://localhost:8081/api/offers/{offer_id}/pdf
```

## 📁 Struktura Projektu

```
internal/offer/
├── generator.go          # Główny generator ofert
├── semantic_analyzer.go  # Analiza semantyczna emaili
├── lg_equipment_db.go    # Baza danych urządzeń LG
├── pdf_generator.go      # Generator PDF
├── handler.go           # HTTP API handlers
├── generator_test.go    # Testy jednostkowe
└── README.md           # Ta dokumentacja

cmd/offer-service/
└── main.go             # Główny serwis

generated_offers/       # Wygenerowane PDF (tworzone automatycznie)
assets/                # Logo, obrazy urządzeń
```

## 🎨 Przykład Wygenerowanej Oferty

### **Nagłówek**
```
FULMARK HVAC
Profesjonalne systemy klimatyzacji
Tel: +48 123 456 789 | www.fulmark.pl

OFERTA KLIMATYZACJI
Oferta nr: HVAC-**********
Data: 21.12.2024
Ważna do: 20.01.2025
```

### **Rekomendacje**
```
OPCJA 1 - LG Dual Cool S18ET
┌─────────────────────────────────────────┐
│ [Zdjęcie]  SPECYFIKACJA:                │
│ urządzenia Moc chłodnicza: 5.0 kW       │
│            Klasa energetyczna: A+++      │
│            Poziom hałasu: 17 dB         │
│            Czynnik chłodniczy: R32      │
│                                         │
│ KLUCZOWE FUNKCJE:                       │
│ Dual Inverter • ThinQ WiFi • Plasmaster│
└─────────────────────────────────────────┘
                                    8700 zł
                                 z instalacją
```

### **Podsumowanie**
```
┌─────────────────────────────────────────┐
│ PODSUMOWANIE OFERTY                     │
│                                         │
│ Koszt urządzeń:              7200 zł    │
│ Koszt instalacji:            1500 zł    │
│ Rabat:                       -500 zł    │
│                                         │
│ CENA KOŃCOWA:                8200 zł    │
└─────────────────────────────────────────┘
```

## 🔧 Konfiguracja Zaawansowana

### **Własne Modele LLM**
```go
// Użyj WatsonX.ai zamiast OpenAI
llm, err := watsonx.New(
    "ibm/granite-13b-instruct-v2",
    watsonx.WithWatsonxAPIKey(apiKey),
    watsonx.WithWatsonxProjectID(projectID),
)
```

### **Dodanie Nowych Urządzeń**
```go
// W lg_equipment_db.go
db.equipment["NEW_MODEL"] = LGEquipment{
    ModelCode: "NEW_MODEL",
    Name:      "LG New Model",
    Capacity:  4.0,
    // ... pozostałe pola
}
```

### **Customizacja PDF**
```go
// W pdf_generator.go
generator.SetLogoPath("./assets/custom_logo.png")
generator.SetOutputDir("./custom_offers")
```

## 🚀 Integracja z Istniejącym Systemem

### **Dodaj do głównego serwera**
```go
// W main.go głównego serwisu
offerHandler, _ := offer.NewOfferHandler()
offerHandler.RegisterRoutes(router)
```

### **Integracja z bazą danych**
```go
// Dodaj do CustomerSemanticAnalyzer
func (a *CustomerSemanticAnalyzer) getCustomerEmails(customerID string) ([]Email, error) {
    // Pobierz emaile z PostgreSQL
    return db.GetCustomerEmails(customerID)
}
```

## 📈 Metryki i Monitoring

- **Czas generowania oferty**: ~2-5 sekund
- **Rozmiar PDF**: ~500KB - 2MB
- **Accuracy dopasowania**: >90% dla kompletnych profili
- **Obsługiwane języki**: Polski (rozszerzalne)

## 🎯 Roadmapa

### **Faza 1 - Podstawy** ✅
- [x] Analiza semantyczna emaili
- [x] Baza urządzeń LG
- [x] Generator PDF
- [x] API endpoints

### **Faza 2 - Rozszerzenia** 🚧
- [ ] Integracja z bazą danych PostgreSQL
- [ ] Więcej modeli urządzeń (Samsung, Daikin)
- [ ] Wielojęzyczność (EN, DE)
- [ ] Dashboard analytics

### **Faza 3 - AI Enhancement** 🔮
- [ ] Personality-driven communication
- [ ] Predictive pricing
- [ ] Image recognition dla pomieszczeń
- [ ] Voice interface

## 🤝 Wsparcie

Masz pytania? Potrzebujesz pomocy?
- 📧 Email: <EMAIL>
- 📱 Tel: +48 123 456 789
- 🌐 Web: www.fulmark.pl

---

**Fulmark HVAC** - 15 lat doświadczenia w klimatyzacji 🌟
