package http

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes registers HTTP routes on the given Gin router.
func RegisterRoutes(router *gin.Engine) {
	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "unified-hvac-crm",
		})
	})

	// TODO: add additional route registrations here, e.g.:
	//	router.GET("/api/customers", customerListHandler)
	//	router.POST("/api/customers", customerCreateHandler)
}
