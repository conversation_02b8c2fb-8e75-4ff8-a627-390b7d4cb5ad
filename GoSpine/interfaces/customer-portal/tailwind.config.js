/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // GoSpine Brand Colors
      colors: {
        // Primary HVAC Colors
        hvac: {
          blue: {
            50: '#eff6ff',
            100: '#dbeafe',
            200: '#bfdbfe',
            300: '#93c5fd',
            400: '#60a5fa',
            500: '#3b82f6',
            600: '#1e40af', // Primary brand color
            700: '#1d4ed8',
            800: '#1e3a8a',
            900: '#1e293b',
          },
          green: {
            50: '#ecfdf5',
            100: '#d1fae5',
            200: '#a7f3d0',
            300: '#6ee7b7',
            400: '#34d399',
            500: '#10b981',
            600: '#059669', // Success color
            700: '#047857',
            800: '#065f46',
            900: '#064e3b',
          },
          orange: {
            50: '#fff7ed',
            100: '#ffedd5',
            200: '#fed7aa',
            300: '#fdba74',
            400: '#fb923c',
            500: '#f97316',
            600: '#ea580c', // Warning color
            700: '#c2410c',
            800: '#9a3412',
            900: '#7c2d12',
          },
          red: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#ef4444',
            600: '#dc2626', // Error color
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
          },
        },
        // Temperature Colors
        temperature: {
          cold: '#3b82f6',
          cool: '#06b6d4',
          warm: '#f59e0b',
          hot: '#ef4444',
        },
        // Efficiency Colors
        efficiency: {
          high: '#10b981',
          medium: '#f59e0b',
          low: '#ef4444',
        },
        // Semantic Colors
        primary: '#1e40af',
        secondary: '#059669',
        accent: '#ea580c',
        danger: '#dc2626',
        warning: '#f59e0b',
        success: '#10b981',
        info: '#3b82f6',
      },
      
      // Typography
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      
      // Golden Ratio Spacing
      spacing: {
        '18': '4.5rem',   // 72px
        '22': '5.5rem',   // 88px
        '26': '6.5rem',   // 104px
        '30': '7.5rem',   // 120px
        '34': '8.5rem',   // 136px
        '38': '9.5rem',   // 152px
        '42': '10.5rem',  // 168px
        '46': '11.5rem',  // 184px
        '50': '12.5rem',  // 200px
        '54': '13.5rem',  // 216px
        '58': '14.5rem',  // 232px
        '62': '15.5rem',  // 248px
        '66': '16.5rem',  // 264px
        '70': '17.5rem',  // 280px
        '74': '18.5rem',  // 296px
        '78': '19.5rem',  // 312px
        '82': '20.5rem',  // 328px
        '86': '21.5rem',  // 344px
        '90': '22.5rem',  // 360px
        '94': '23.5rem',  // 376px
        '98': '24.5rem',  // 392px
      },
      
      // Border Radius (Golden Ratio)
      borderRadius: {
        'xs': '0.125rem',
        'sm': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
        '4xl': '2rem',
      },
      
      // Box Shadow
      boxShadow: {
        'hvac-sm': '0 1px 2px 0 rgba(30, 64, 175, 0.05)',
        'hvac-md': '0 4px 6px -1px rgba(30, 64, 175, 0.1), 0 2px 4px -1px rgba(30, 64, 175, 0.06)',
        'hvac-lg': '0 10px 15px -3px rgba(30, 64, 175, 0.1), 0 4px 6px -2px rgba(30, 64, 175, 0.05)',
        'hvac-xl': '0 20px 25px -5px rgba(30, 64, 175, 0.1), 0 10px 10px -5px rgba(30, 64, 175, 0.04)',
        'hvac-2xl': '0 25px 50px -12px rgba(30, 64, 175, 0.25)',
        'hvac-inner': 'inset 0 2px 4px 0 rgba(30, 64, 175, 0.06)',
      },
      
      // Animation
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
        'spin-slow': 'spin 3s linear infinite',
      },
      
      // Keyframes
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      
      // Screens (Responsive Breakpoints)
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        '3xl': '1920px',
      },
      
      // Z-Index
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
      
      // Backdrop Blur
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '24px',
        '3xl': '40px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    
    // Custom plugin for HVAC utilities
    function({ addUtilities, addComponents, theme }) {
      // HVAC specific utilities
      addUtilities({
        '.text-temperature-cold': {
          color: theme('colors.temperature.cold'),
        },
        '.text-temperature-warm': {
          color: theme('colors.temperature.warm'),
        },
        '.text-temperature-hot': {
          color: theme('colors.temperature.hot'),
        },
        '.bg-efficiency-high': {
          backgroundColor: theme('colors.efficiency.high'),
        },
        '.bg-efficiency-medium': {
          backgroundColor: theme('colors.efficiency.medium'),
        },
        '.bg-efficiency-low': {
          backgroundColor: theme('colors.efficiency.low'),
        },
      });
      
      // HVAC components
      addComponents({
        '.hvac-card': {
          backgroundColor: theme('colors.white'),
          borderRadius: theme('borderRadius.lg'),
          boxShadow: theme('boxShadow.hvac-md'),
          padding: theme('spacing.6'),
          border: `1px solid ${theme('colors.gray.200')}`,
        },
        '.hvac-button': {
          backgroundColor: theme('colors.hvac.blue.600'),
          color: theme('colors.white'),
          padding: `${theme('spacing.2')} ${theme('spacing.4')}`,
          borderRadius: theme('borderRadius.md'),
          fontWeight: theme('fontWeight.medium'),
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            backgroundColor: theme('colors.hvac.blue.700'),
            transform: 'translateY(-1px)',
            boxShadow: theme('boxShadow.hvac-lg'),
          },
        },
        '.hvac-input': {
          borderColor: theme('colors.gray.300'),
          borderRadius: theme('borderRadius.md'),
          padding: theme('spacing.3'),
          fontSize: theme('fontSize.sm'),
          '&:focus': {
            borderColor: theme('colors.hvac.blue.500'),
            boxShadow: `0 0 0 3px ${theme('colors.hvac.blue.100')}`,
          },
        },
      });
    },
  ],
  
  // Dark mode
  darkMode: 'class',
};
