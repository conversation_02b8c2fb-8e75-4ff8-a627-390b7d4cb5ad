{"name": "gospine-customer-portal", "version": "1.0.0", "description": "GoSpine Customer Self-Service Portal - The most advanced HVAC customer experience", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true next build", "docker:build": "docker build -t gospine/customer-portal .", "docker:run": "docker run -p 3000:3000 gospine/customer-portal"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.17.0", "zustand": "^4.4.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "framer-motion": "^10.18.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^3.0.6", "recharts": "^2.9.0", "lucide-react": "^0.303.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "react-hot-toast": "^2.4.1", "next-auth": "^4.24.5", "jose": "^5.2.0", "react-day-picker": "^8.10.0", "react-dropzone": "^14.2.3", "react-intersection-observer": "^9.5.3", "use-debounce": "^10.0.0"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@playwright/test": "^1.40.1", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-jsx-a11y": "^6.8.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "keywords": ["hvac", "crm", "customer-portal", "nextjs", "react", "typescript", "tailwindcss", "gospine"], "author": {"name": "GoSpine Development Team", "email": "<EMAIL>", "url": "https://gospine.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/gospine/customer-portal.git"}, "bugs": {"url": "https://github.com/gospine/customer-portal/issues"}, "homepage": "https://portal.gospine.com"}