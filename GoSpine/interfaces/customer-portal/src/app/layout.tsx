import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers/Providers';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    template: '%s | GoSpine Customer Portal',
    default: 'GoSpine Customer Portal - Advanced HVAC Management',
  },
  description: 'The most advanced HVAC customer portal in Europe. Manage your heating, ventilation, and air conditioning services with ease.',
  keywords: [
    'HVAC',
    'heating',
    'ventilation',
    'air conditioning',
    'customer portal',
    'service management',
    'equipment monitoring',
    'energy efficiency',
  ],
  authors: [{ name: 'GoSpine Development Team', url: 'https://gospine.com' }],
  creator: 'GoSpine',
  publisher: 'GoSpine',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://portal.gospine.com'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en-US',
      'pl-PL': '/pl-PL',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://portal.gospine.com',
    title: 'GoSpine Customer Portal',
    description: 'The most advanced HVAC customer portal in Europe',
    siteName: 'GoSpine',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'GoSpine Customer Portal',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GoSpine Customer Portal',
    description: 'The most advanced HVAC customer portal in Europe',
    images: ['/twitter-image.png'],
    creator: '@gospine',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#1e40af" />
        <meta name="color-scheme" content="light dark" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <Providers>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {children}
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
