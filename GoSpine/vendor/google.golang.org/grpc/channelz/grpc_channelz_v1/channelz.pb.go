// Copyright 2018 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file defines an interface for exporting monitoring information
// out of gRPC servers.  See the full design at
// https://github.com/grpc/proposal/blob/master/A14-channelz.md
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/channelz/v1/channelz.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.1
// source: grpc/channelz/v1/channelz.proto

package grpc_channelz_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChannelConnectivityState_State int32

const (
	ChannelConnectivityState_UNKNOWN           ChannelConnectivityState_State = 0
	ChannelConnectivityState_IDLE              ChannelConnectivityState_State = 1
	ChannelConnectivityState_CONNECTING        ChannelConnectivityState_State = 2
	ChannelConnectivityState_READY             ChannelConnectivityState_State = 3
	ChannelConnectivityState_TRANSIENT_FAILURE ChannelConnectivityState_State = 4
	ChannelConnectivityState_SHUTDOWN          ChannelConnectivityState_State = 5
)

// Enum value maps for ChannelConnectivityState_State.
var (
	ChannelConnectivityState_State_name = map[int32]string{
		0: "UNKNOWN",
		1: "IDLE",
		2: "CONNECTING",
		3: "READY",
		4: "TRANSIENT_FAILURE",
		5: "SHUTDOWN",
	}
	ChannelConnectivityState_State_value = map[string]int32{
		"UNKNOWN":           0,
		"IDLE":              1,
		"CONNECTING":        2,
		"READY":             3,
		"TRANSIENT_FAILURE": 4,
		"SHUTDOWN":          5,
	}
)

func (x ChannelConnectivityState_State) Enum() *ChannelConnectivityState_State {
	p := new(ChannelConnectivityState_State)
	*p = x
	return p
}

func (x ChannelConnectivityState_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelConnectivityState_State) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_channelz_v1_channelz_proto_enumTypes[0].Descriptor()
}

func (ChannelConnectivityState_State) Type() protoreflect.EnumType {
	return &file_grpc_channelz_v1_channelz_proto_enumTypes[0]
}

func (x ChannelConnectivityState_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelConnectivityState_State.Descriptor instead.
func (ChannelConnectivityState_State) EnumDescriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{2, 0}
}

// The supported severity levels of trace events.
type ChannelTraceEvent_Severity int32

const (
	ChannelTraceEvent_CT_UNKNOWN ChannelTraceEvent_Severity = 0
	ChannelTraceEvent_CT_INFO    ChannelTraceEvent_Severity = 1
	ChannelTraceEvent_CT_WARNING ChannelTraceEvent_Severity = 2
	ChannelTraceEvent_CT_ERROR   ChannelTraceEvent_Severity = 3
)

// Enum value maps for ChannelTraceEvent_Severity.
var (
	ChannelTraceEvent_Severity_name = map[int32]string{
		0: "CT_UNKNOWN",
		1: "CT_INFO",
		2: "CT_WARNING",
		3: "CT_ERROR",
	}
	ChannelTraceEvent_Severity_value = map[string]int32{
		"CT_UNKNOWN": 0,
		"CT_INFO":    1,
		"CT_WARNING": 2,
		"CT_ERROR":   3,
	}
)

func (x ChannelTraceEvent_Severity) Enum() *ChannelTraceEvent_Severity {
	p := new(ChannelTraceEvent_Severity)
	*p = x
	return p
}

func (x ChannelTraceEvent_Severity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelTraceEvent_Severity) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_channelz_v1_channelz_proto_enumTypes[1].Descriptor()
}

func (ChannelTraceEvent_Severity) Type() protoreflect.EnumType {
	return &file_grpc_channelz_v1_channelz_proto_enumTypes[1]
}

func (x ChannelTraceEvent_Severity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelTraceEvent_Severity.Descriptor instead.
func (ChannelTraceEvent_Severity) EnumDescriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{4, 0}
}

// Channel is a logical grouping of channels, subchannels, and sockets.
type Channel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifier for this channel. This should be set.
	Ref *ChannelRef `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	// Data specific to this channel.
	Data *ChannelData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // At most one of 'channel_ref+subchannel_ref' and 'socket' is set.
	// There are no ordering guarantees on the order of channel refs.
	// There may not be cycles in the ref graph.
	// A channel ref may be present in more than one channel or subchannel.
	ChannelRef []*ChannelRef `protobuf:"bytes,3,rep,name=channel_ref,json=channelRef,proto3" json:"channel_ref,omitempty"`
	// At most one of 'channel_ref+subchannel_ref' and 'socket' is set.
	// There are no ordering guarantees on the order of subchannel refs.
	// There may not be cycles in the ref graph.
	// A sub channel ref may be present in more than one channel or subchannel.
	SubchannelRef []*SubchannelRef `protobuf:"bytes,4,rep,name=subchannel_ref,json=subchannelRef,proto3" json:"subchannel_ref,omitempty"`
	// There are no ordering guarantees on the order of sockets.
	SocketRef []*SocketRef `protobuf:"bytes,5,rep,name=socket_ref,json=socketRef,proto3" json:"socket_ref,omitempty"`
}

func (x *Channel) Reset() {
	*x = Channel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Channel) ProtoMessage() {}

func (x *Channel) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Channel.ProtoReflect.Descriptor instead.
func (*Channel) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{0}
}

func (x *Channel) GetRef() *ChannelRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *Channel) GetData() *ChannelData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Channel) GetChannelRef() []*ChannelRef {
	if x != nil {
		return x.ChannelRef
	}
	return nil
}

func (x *Channel) GetSubchannelRef() []*SubchannelRef {
	if x != nil {
		return x.SubchannelRef
	}
	return nil
}

func (x *Channel) GetSocketRef() []*SocketRef {
	if x != nil {
		return x.SocketRef
	}
	return nil
}

// Subchannel is a logical grouping of channels, subchannels, and sockets.
// A subchannel is load balanced over by its ancestor
type Subchannel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifier for this channel.
	Ref *SubchannelRef `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	// Data specific to this channel.
	Data *ChannelData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // At most one of 'channel_ref+subchannel_ref' and 'socket' is set.
	// There are no ordering guarantees on the order of channel refs.
	// There may not be cycles in the ref graph.
	// A channel ref may be present in more than one channel or subchannel.
	ChannelRef []*ChannelRef `protobuf:"bytes,3,rep,name=channel_ref,json=channelRef,proto3" json:"channel_ref,omitempty"`
	// At most one of 'channel_ref+subchannel_ref' and 'socket' is set.
	// There are no ordering guarantees on the order of subchannel refs.
	// There may not be cycles in the ref graph.
	// A sub channel ref may be present in more than one channel or subchannel.
	SubchannelRef []*SubchannelRef `protobuf:"bytes,4,rep,name=subchannel_ref,json=subchannelRef,proto3" json:"subchannel_ref,omitempty"`
	// There are no ordering guarantees on the order of sockets.
	SocketRef []*SocketRef `protobuf:"bytes,5,rep,name=socket_ref,json=socketRef,proto3" json:"socket_ref,omitempty"`
}

func (x *Subchannel) Reset() {
	*x = Subchannel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subchannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subchannel) ProtoMessage() {}

func (x *Subchannel) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subchannel.ProtoReflect.Descriptor instead.
func (*Subchannel) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{1}
}

func (x *Subchannel) GetRef() *SubchannelRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *Subchannel) GetData() *ChannelData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Subchannel) GetChannelRef() []*ChannelRef {
	if x != nil {
		return x.ChannelRef
	}
	return nil
}

func (x *Subchannel) GetSubchannelRef() []*SubchannelRef {
	if x != nil {
		return x.SubchannelRef
	}
	return nil
}

func (x *Subchannel) GetSocketRef() []*SocketRef {
	if x != nil {
		return x.SocketRef
	}
	return nil
}

// These come from the specified states in this document:
// https://github.com/grpc/grpc/blob/master/doc/connectivity-semantics-and-api.md
type ChannelConnectivityState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State ChannelConnectivityState_State `protobuf:"varint,1,opt,name=state,proto3,enum=grpc.channelz.v1.ChannelConnectivityState_State" json:"state,omitempty"`
}

func (x *ChannelConnectivityState) Reset() {
	*x = ChannelConnectivityState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelConnectivityState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelConnectivityState) ProtoMessage() {}

func (x *ChannelConnectivityState) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelConnectivityState.ProtoReflect.Descriptor instead.
func (*ChannelConnectivityState) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{2}
}

func (x *ChannelConnectivityState) GetState() ChannelConnectivityState_State {
	if x != nil {
		return x.State
	}
	return ChannelConnectivityState_UNKNOWN
}

// Channel data is data related to a specific Channel or Subchannel.
type ChannelData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The connectivity state of the channel or subchannel.  Implementations
	// should always set this.
	State *ChannelConnectivityState `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	// The target this channel originally tried to connect to.  May be absent
	Target string `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	// A trace of recent events on the channel.  May be absent.
	Trace *ChannelTrace `protobuf:"bytes,3,opt,name=trace,proto3" json:"trace,omitempty"`
	// The number of calls started on the channel
	CallsStarted int64 `protobuf:"varint,4,opt,name=calls_started,json=callsStarted,proto3" json:"calls_started,omitempty"`
	// The number of calls that have completed with an OK status
	CallsSucceeded int64 `protobuf:"varint,5,opt,name=calls_succeeded,json=callsSucceeded,proto3" json:"calls_succeeded,omitempty"`
	// The number of calls that have completed with a non-OK status
	CallsFailed int64 `protobuf:"varint,6,opt,name=calls_failed,json=callsFailed,proto3" json:"calls_failed,omitempty"`
	// The last time a call was started on the channel.
	LastCallStartedTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_call_started_timestamp,json=lastCallStartedTimestamp,proto3" json:"last_call_started_timestamp,omitempty"`
}

func (x *ChannelData) Reset() {
	*x = ChannelData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelData) ProtoMessage() {}

func (x *ChannelData) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelData.ProtoReflect.Descriptor instead.
func (*ChannelData) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{3}
}

func (x *ChannelData) GetState() *ChannelConnectivityState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *ChannelData) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *ChannelData) GetTrace() *ChannelTrace {
	if x != nil {
		return x.Trace
	}
	return nil
}

func (x *ChannelData) GetCallsStarted() int64 {
	if x != nil {
		return x.CallsStarted
	}
	return 0
}

func (x *ChannelData) GetCallsSucceeded() int64 {
	if x != nil {
		return x.CallsSucceeded
	}
	return 0
}

func (x *ChannelData) GetCallsFailed() int64 {
	if x != nil {
		return x.CallsFailed
	}
	return 0
}

func (x *ChannelData) GetLastCallStartedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCallStartedTimestamp
	}
	return nil
}

// A trace event is an interesting thing that happened to a channel or
// subchannel, such as creation, address resolution, subchannel creation, etc.
type ChannelTraceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// High level description of the event.
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// the severity of the trace event
	Severity ChannelTraceEvent_Severity `protobuf:"varint,2,opt,name=severity,proto3,enum=grpc.channelz.v1.ChannelTraceEvent_Severity" json:"severity,omitempty"`
	// When this event occurred.
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// ref of referenced channel or subchannel.
	// Optional, only present if this event refers to a child object. For example,
	// this field would be filled if this trace event was for a subchannel being
	// created.
	//
	// Types that are assignable to ChildRef:
	//
	//	*ChannelTraceEvent_ChannelRef
	//	*ChannelTraceEvent_SubchannelRef
	ChildRef isChannelTraceEvent_ChildRef `protobuf_oneof:"child_ref"`
}

func (x *ChannelTraceEvent) Reset() {
	*x = ChannelTraceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelTraceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelTraceEvent) ProtoMessage() {}

func (x *ChannelTraceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelTraceEvent.ProtoReflect.Descriptor instead.
func (*ChannelTraceEvent) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{4}
}

func (x *ChannelTraceEvent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ChannelTraceEvent) GetSeverity() ChannelTraceEvent_Severity {
	if x != nil {
		return x.Severity
	}
	return ChannelTraceEvent_CT_UNKNOWN
}

func (x *ChannelTraceEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (m *ChannelTraceEvent) GetChildRef() isChannelTraceEvent_ChildRef {
	if m != nil {
		return m.ChildRef
	}
	return nil
}

func (x *ChannelTraceEvent) GetChannelRef() *ChannelRef {
	if x, ok := x.GetChildRef().(*ChannelTraceEvent_ChannelRef); ok {
		return x.ChannelRef
	}
	return nil
}

func (x *ChannelTraceEvent) GetSubchannelRef() *SubchannelRef {
	if x, ok := x.GetChildRef().(*ChannelTraceEvent_SubchannelRef); ok {
		return x.SubchannelRef
	}
	return nil
}

type isChannelTraceEvent_ChildRef interface {
	isChannelTraceEvent_ChildRef()
}

type ChannelTraceEvent_ChannelRef struct {
	ChannelRef *ChannelRef `protobuf:"bytes,4,opt,name=channel_ref,json=channelRef,proto3,oneof"`
}

type ChannelTraceEvent_SubchannelRef struct {
	SubchannelRef *SubchannelRef `protobuf:"bytes,5,opt,name=subchannel_ref,json=subchannelRef,proto3,oneof"`
}

func (*ChannelTraceEvent_ChannelRef) isChannelTraceEvent_ChildRef() {}

func (*ChannelTraceEvent_SubchannelRef) isChannelTraceEvent_ChildRef() {}

// ChannelTrace represents the recent events that have occurred on the channel.
type ChannelTrace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of events ever logged in this tracing object. This can differ from
	// events.size() because events can be overwritten or garbage collected by
	// implementations.
	NumEventsLogged int64 `protobuf:"varint,1,opt,name=num_events_logged,json=numEventsLogged,proto3" json:"num_events_logged,omitempty"`
	// Time that this channel was created.
	CreationTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=creation_timestamp,json=creationTimestamp,proto3" json:"creation_timestamp,omitempty"`
	// List of events that have occurred on this channel.
	Events []*ChannelTraceEvent `protobuf:"bytes,3,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *ChannelTrace) Reset() {
	*x = ChannelTrace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelTrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelTrace) ProtoMessage() {}

func (x *ChannelTrace) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelTrace.ProtoReflect.Descriptor instead.
func (*ChannelTrace) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{5}
}

func (x *ChannelTrace) GetNumEventsLogged() int64 {
	if x != nil {
		return x.NumEventsLogged
	}
	return 0
}

func (x *ChannelTrace) GetCreationTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationTimestamp
	}
	return nil
}

func (x *ChannelTrace) GetEvents() []*ChannelTraceEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

// ChannelRef is a reference to a Channel.
type ChannelRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The globally unique id for this channel.  Must be a positive number.
	ChannelId int64 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// An optional name associated with the channel.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ChannelRef) Reset() {
	*x = ChannelRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelRef) ProtoMessage() {}

func (x *ChannelRef) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelRef.ProtoReflect.Descriptor instead.
func (*ChannelRef) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{6}
}

func (x *ChannelRef) GetChannelId() int64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ChannelRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SubchannelRef is a reference to a Subchannel.
type SubchannelRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The globally unique id for this subchannel.  Must be a positive number.
	SubchannelId int64 `protobuf:"varint,7,opt,name=subchannel_id,json=subchannelId,proto3" json:"subchannel_id,omitempty"`
	// An optional name associated with the subchannel.
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SubchannelRef) Reset() {
	*x = SubchannelRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubchannelRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubchannelRef) ProtoMessage() {}

func (x *SubchannelRef) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubchannelRef.ProtoReflect.Descriptor instead.
func (*SubchannelRef) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{7}
}

func (x *SubchannelRef) GetSubchannelId() int64 {
	if x != nil {
		return x.SubchannelId
	}
	return 0
}

func (x *SubchannelRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SocketRef is a reference to a Socket.
type SocketRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The globally unique id for this socket.  Must be a positive number.
	SocketId int64 `protobuf:"varint,3,opt,name=socket_id,json=socketId,proto3" json:"socket_id,omitempty"`
	// An optional name associated with the socket.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SocketRef) Reset() {
	*x = SocketRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketRef) ProtoMessage() {}

func (x *SocketRef) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketRef.ProtoReflect.Descriptor instead.
func (*SocketRef) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{8}
}

func (x *SocketRef) GetSocketId() int64 {
	if x != nil {
		return x.SocketId
	}
	return 0
}

func (x *SocketRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ServerRef is a reference to a Server.
type ServerRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A globally unique identifier for this server.  Must be a positive number.
	ServerId int64 `protobuf:"varint,5,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	// An optional name associated with the server.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ServerRef) Reset() {
	*x = ServerRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerRef) ProtoMessage() {}

func (x *ServerRef) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerRef.ProtoReflect.Descriptor instead.
func (*ServerRef) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{9}
}

func (x *ServerRef) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ServerRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Server represents a single server.  There may be multiple servers in a single
// program.
type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifier for a Server.  This should be set.
	Ref *ServerRef `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	// The associated data of the Server.
	Data *ServerData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// The sockets that the server is listening on.  There are no ordering
	// guarantees.  This may be absent.
	ListenSocket []*SocketRef `protobuf:"bytes,3,rep,name=listen_socket,json=listenSocket,proto3" json:"listen_socket,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{10}
}

func (x *Server) GetRef() *ServerRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *Server) GetData() *ServerData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Server) GetListenSocket() []*SocketRef {
	if x != nil {
		return x.ListenSocket
	}
	return nil
}

// ServerData is data for a specific Server.
type ServerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A trace of recent events on the server.  May be absent.
	Trace *ChannelTrace `protobuf:"bytes,1,opt,name=trace,proto3" json:"trace,omitempty"`
	// The number of incoming calls started on the server
	CallsStarted int64 `protobuf:"varint,2,opt,name=calls_started,json=callsStarted,proto3" json:"calls_started,omitempty"`
	// The number of incoming calls that have completed with an OK status
	CallsSucceeded int64 `protobuf:"varint,3,opt,name=calls_succeeded,json=callsSucceeded,proto3" json:"calls_succeeded,omitempty"`
	// The number of incoming calls that have a completed with a non-OK status
	CallsFailed int64 `protobuf:"varint,4,opt,name=calls_failed,json=callsFailed,proto3" json:"calls_failed,omitempty"`
	// The last time a call was started on the server.
	LastCallStartedTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_call_started_timestamp,json=lastCallStartedTimestamp,proto3" json:"last_call_started_timestamp,omitempty"`
}

func (x *ServerData) Reset() {
	*x = ServerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerData) ProtoMessage() {}

func (x *ServerData) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerData.ProtoReflect.Descriptor instead.
func (*ServerData) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{11}
}

func (x *ServerData) GetTrace() *ChannelTrace {
	if x != nil {
		return x.Trace
	}
	return nil
}

func (x *ServerData) GetCallsStarted() int64 {
	if x != nil {
		return x.CallsStarted
	}
	return 0
}

func (x *ServerData) GetCallsSucceeded() int64 {
	if x != nil {
		return x.CallsSucceeded
	}
	return 0
}

func (x *ServerData) GetCallsFailed() int64 {
	if x != nil {
		return x.CallsFailed
	}
	return 0
}

func (x *ServerData) GetLastCallStartedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCallStartedTimestamp
	}
	return nil
}

// Information about an actual connection.  Pronounced "sock-ay".
type Socket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifier for the Socket.
	Ref *SocketRef `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	// Data specific to this Socket.
	Data *SocketData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// The locally bound address.
	Local *Address `protobuf:"bytes,3,opt,name=local,proto3" json:"local,omitempty"`
	// The remote bound address.  May be absent.
	Remote *Address `protobuf:"bytes,4,opt,name=remote,proto3" json:"remote,omitempty"`
	// Security details for this socket.  May be absent if not available, or
	// there is no security on the socket.
	Security *Security `protobuf:"bytes,5,opt,name=security,proto3" json:"security,omitempty"`
	// Optional, represents the name of the remote endpoint, if different than
	// the original target name.
	RemoteName string `protobuf:"bytes,6,opt,name=remote_name,json=remoteName,proto3" json:"remote_name,omitempty"`
}

func (x *Socket) Reset() {
	*x = Socket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Socket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Socket) ProtoMessage() {}

func (x *Socket) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Socket.ProtoReflect.Descriptor instead.
func (*Socket) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{12}
}

func (x *Socket) GetRef() *SocketRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *Socket) GetData() *SocketData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Socket) GetLocal() *Address {
	if x != nil {
		return x.Local
	}
	return nil
}

func (x *Socket) GetRemote() *Address {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *Socket) GetSecurity() *Security {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *Socket) GetRemoteName() string {
	if x != nil {
		return x.RemoteName
	}
	return ""
}

// SocketData is data associated for a specific Socket.  The fields present
// are specific to the implementation, so there may be minor differences in
// the semantics.  (e.g. flow control windows)
type SocketData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of streams that have been started.
	StreamsStarted int64 `protobuf:"varint,1,opt,name=streams_started,json=streamsStarted,proto3" json:"streams_started,omitempty"`
	// The number of streams that have ended successfully:
	// On client side, received frame with eos bit set;
	// On server side, sent frame with eos bit set.
	StreamsSucceeded int64 `protobuf:"varint,2,opt,name=streams_succeeded,json=streamsSucceeded,proto3" json:"streams_succeeded,omitempty"`
	// The number of streams that have ended unsuccessfully:
	// On client side, ended without receiving frame with eos bit set;
	// On server side, ended without sending frame with eos bit set.
	StreamsFailed int64 `protobuf:"varint,3,opt,name=streams_failed,json=streamsFailed,proto3" json:"streams_failed,omitempty"`
	// The number of grpc messages successfully sent on this socket.
	MessagesSent int64 `protobuf:"varint,4,opt,name=messages_sent,json=messagesSent,proto3" json:"messages_sent,omitempty"`
	// The number of grpc messages received on this socket.
	MessagesReceived int64 `protobuf:"varint,5,opt,name=messages_received,json=messagesReceived,proto3" json:"messages_received,omitempty"`
	// The number of keep alives sent.  This is typically implemented with HTTP/2
	// ping messages.
	KeepAlivesSent int64 `protobuf:"varint,6,opt,name=keep_alives_sent,json=keepAlivesSent,proto3" json:"keep_alives_sent,omitempty"`
	// The last time a stream was created by this endpoint.  Usually unset for
	// servers.
	LastLocalStreamCreatedTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_local_stream_created_timestamp,json=lastLocalStreamCreatedTimestamp,proto3" json:"last_local_stream_created_timestamp,omitempty"`
	// The last time a stream was created by the remote endpoint.  Usually unset
	// for clients.
	LastRemoteStreamCreatedTimestamp *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_remote_stream_created_timestamp,json=lastRemoteStreamCreatedTimestamp,proto3" json:"last_remote_stream_created_timestamp,omitempty"`
	// The last time a message was sent by this endpoint.
	LastMessageSentTimestamp *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_message_sent_timestamp,json=lastMessageSentTimestamp,proto3" json:"last_message_sent_timestamp,omitempty"`
	// The last time a message was received by this endpoint.
	LastMessageReceivedTimestamp *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_message_received_timestamp,json=lastMessageReceivedTimestamp,proto3" json:"last_message_received_timestamp,omitempty"`
	// The amount of window, granted to the local endpoint by the remote endpoint.
	// This may be slightly out of date due to network latency.  This does NOT
	// include stream level or TCP level flow control info.
	LocalFlowControlWindow *wrapperspb.Int64Value `protobuf:"bytes,11,opt,name=local_flow_control_window,json=localFlowControlWindow,proto3" json:"local_flow_control_window,omitempty"`
	// The amount of window, granted to the remote endpoint by the local endpoint.
	// This may be slightly out of date due to network latency.  This does NOT
	// include stream level or TCP level flow control info.
	RemoteFlowControlWindow *wrapperspb.Int64Value `protobuf:"bytes,12,opt,name=remote_flow_control_window,json=remoteFlowControlWindow,proto3" json:"remote_flow_control_window,omitempty"`
	// Socket options set on this socket.  May be absent if 'summary' is set
	// on GetSocketRequest.
	Option []*SocketOption `protobuf:"bytes,13,rep,name=option,proto3" json:"option,omitempty"`
}

func (x *SocketData) Reset() {
	*x = SocketData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketData) ProtoMessage() {}

func (x *SocketData) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketData.ProtoReflect.Descriptor instead.
func (*SocketData) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{13}
}

func (x *SocketData) GetStreamsStarted() int64 {
	if x != nil {
		return x.StreamsStarted
	}
	return 0
}

func (x *SocketData) GetStreamsSucceeded() int64 {
	if x != nil {
		return x.StreamsSucceeded
	}
	return 0
}

func (x *SocketData) GetStreamsFailed() int64 {
	if x != nil {
		return x.StreamsFailed
	}
	return 0
}

func (x *SocketData) GetMessagesSent() int64 {
	if x != nil {
		return x.MessagesSent
	}
	return 0
}

func (x *SocketData) GetMessagesReceived() int64 {
	if x != nil {
		return x.MessagesReceived
	}
	return 0
}

func (x *SocketData) GetKeepAlivesSent() int64 {
	if x != nil {
		return x.KeepAlivesSent
	}
	return 0
}

func (x *SocketData) GetLastLocalStreamCreatedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastLocalStreamCreatedTimestamp
	}
	return nil
}

func (x *SocketData) GetLastRemoteStreamCreatedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRemoteStreamCreatedTimestamp
	}
	return nil
}

func (x *SocketData) GetLastMessageSentTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastMessageSentTimestamp
	}
	return nil
}

func (x *SocketData) GetLastMessageReceivedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastMessageReceivedTimestamp
	}
	return nil
}

func (x *SocketData) GetLocalFlowControlWindow() *wrapperspb.Int64Value {
	if x != nil {
		return x.LocalFlowControlWindow
	}
	return nil
}

func (x *SocketData) GetRemoteFlowControlWindow() *wrapperspb.Int64Value {
	if x != nil {
		return x.RemoteFlowControlWindow
	}
	return nil
}

func (x *SocketData) GetOption() []*SocketOption {
	if x != nil {
		return x.Option
	}
	return nil
}

// Address represents the address used to create the socket.
type Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Address:
	//
	//	*Address_TcpipAddress
	//	*Address_UdsAddress_
	//	*Address_OtherAddress_
	Address isAddress_Address `protobuf_oneof:"address"`
}

func (x *Address) Reset() {
	*x = Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{14}
}

func (m *Address) GetAddress() isAddress_Address {
	if m != nil {
		return m.Address
	}
	return nil
}

func (x *Address) GetTcpipAddress() *Address_TcpIpAddress {
	if x, ok := x.GetAddress().(*Address_TcpipAddress); ok {
		return x.TcpipAddress
	}
	return nil
}

func (x *Address) GetUdsAddress() *Address_UdsAddress {
	if x, ok := x.GetAddress().(*Address_UdsAddress_); ok {
		return x.UdsAddress
	}
	return nil
}

func (x *Address) GetOtherAddress() *Address_OtherAddress {
	if x, ok := x.GetAddress().(*Address_OtherAddress_); ok {
		return x.OtherAddress
	}
	return nil
}

type isAddress_Address interface {
	isAddress_Address()
}

type Address_TcpipAddress struct {
	TcpipAddress *Address_TcpIpAddress `protobuf:"bytes,1,opt,name=tcpip_address,json=tcpipAddress,proto3,oneof"`
}

type Address_UdsAddress_ struct {
	UdsAddress *Address_UdsAddress `protobuf:"bytes,2,opt,name=uds_address,json=udsAddress,proto3,oneof"`
}

type Address_OtherAddress_ struct {
	OtherAddress *Address_OtherAddress `protobuf:"bytes,3,opt,name=other_address,json=otherAddress,proto3,oneof"`
}

func (*Address_TcpipAddress) isAddress_Address() {}

func (*Address_UdsAddress_) isAddress_Address() {}

func (*Address_OtherAddress_) isAddress_Address() {}

// Security represents details about how secure the socket is.
type Security struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Model:
	//
	//	*Security_Tls_
	//	*Security_Other
	Model isSecurity_Model `protobuf_oneof:"model"`
}

func (x *Security) Reset() {
	*x = Security{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Security) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Security) ProtoMessage() {}

func (x *Security) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Security.ProtoReflect.Descriptor instead.
func (*Security) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{15}
}

func (m *Security) GetModel() isSecurity_Model {
	if m != nil {
		return m.Model
	}
	return nil
}

func (x *Security) GetTls() *Security_Tls {
	if x, ok := x.GetModel().(*Security_Tls_); ok {
		return x.Tls
	}
	return nil
}

func (x *Security) GetOther() *Security_OtherSecurity {
	if x, ok := x.GetModel().(*Security_Other); ok {
		return x.Other
	}
	return nil
}

type isSecurity_Model interface {
	isSecurity_Model()
}

type Security_Tls_ struct {
	Tls *Security_Tls `protobuf:"bytes,1,opt,name=tls,proto3,oneof"`
}

type Security_Other struct {
	Other *Security_OtherSecurity `protobuf:"bytes,2,opt,name=other,proto3,oneof"`
}

func (*Security_Tls_) isSecurity_Model() {}

func (*Security_Other) isSecurity_Model() {}

// SocketOption represents socket options for a socket.  Specifically, these
// are the options returned by getsockopt().
type SocketOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The full name of the socket option.  Typically this will be the upper case
	// name, such as "SO_REUSEPORT".
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The human readable value of this socket option.  At least one of value or
	// additional will be set.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Additional data associated with the socket option.  At least one of value
	// or additional will be set.
	Additional *anypb.Any `protobuf:"bytes,3,opt,name=additional,proto3" json:"additional,omitempty"`
}

func (x *SocketOption) Reset() {
	*x = SocketOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketOption) ProtoMessage() {}

func (x *SocketOption) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketOption.ProtoReflect.Descriptor instead.
func (*SocketOption) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{16}
}

func (x *SocketOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SocketOption) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SocketOption) GetAdditional() *anypb.Any {
	if x != nil {
		return x.Additional
	}
	return nil
}

// For use with SocketOption's additional field.  This is primarily used for
// SO_RCVTIMEO and SO_SNDTIMEO
type SocketOptionTimeout struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration *durationpb.Duration `protobuf:"bytes,1,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *SocketOptionTimeout) Reset() {
	*x = SocketOptionTimeout{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketOptionTimeout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketOptionTimeout) ProtoMessage() {}

func (x *SocketOptionTimeout) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketOptionTimeout.ProtoReflect.Descriptor instead.
func (*SocketOptionTimeout) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{17}
}

func (x *SocketOptionTimeout) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

// For use with SocketOption's additional field.  This is primarily used for
// SO_LINGER.
type SocketOptionLinger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// active maps to `struct linger.l_onoff`
	Active bool `protobuf:"varint,1,opt,name=active,proto3" json:"active,omitempty"`
	// duration maps to `struct linger.l_linger`
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *SocketOptionLinger) Reset() {
	*x = SocketOptionLinger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketOptionLinger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketOptionLinger) ProtoMessage() {}

func (x *SocketOptionLinger) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketOptionLinger.ProtoReflect.Descriptor instead.
func (*SocketOptionLinger) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{18}
}

func (x *SocketOptionLinger) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *SocketOptionLinger) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

// For use with SocketOption's additional field.  Tcp info for
// SOL_TCP and TCP_INFO.
type SocketOptionTcpInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TcpiState        uint32 `protobuf:"varint,1,opt,name=tcpi_state,json=tcpiState,proto3" json:"tcpi_state,omitempty"`
	TcpiCaState      uint32 `protobuf:"varint,2,opt,name=tcpi_ca_state,json=tcpiCaState,proto3" json:"tcpi_ca_state,omitempty"`
	TcpiRetransmits  uint32 `protobuf:"varint,3,opt,name=tcpi_retransmits,json=tcpiRetransmits,proto3" json:"tcpi_retransmits,omitempty"`
	TcpiProbes       uint32 `protobuf:"varint,4,opt,name=tcpi_probes,json=tcpiProbes,proto3" json:"tcpi_probes,omitempty"`
	TcpiBackoff      uint32 `protobuf:"varint,5,opt,name=tcpi_backoff,json=tcpiBackoff,proto3" json:"tcpi_backoff,omitempty"`
	TcpiOptions      uint32 `protobuf:"varint,6,opt,name=tcpi_options,json=tcpiOptions,proto3" json:"tcpi_options,omitempty"`
	TcpiSndWscale    uint32 `protobuf:"varint,7,opt,name=tcpi_snd_wscale,json=tcpiSndWscale,proto3" json:"tcpi_snd_wscale,omitempty"`
	TcpiRcvWscale    uint32 `protobuf:"varint,8,opt,name=tcpi_rcv_wscale,json=tcpiRcvWscale,proto3" json:"tcpi_rcv_wscale,omitempty"`
	TcpiRto          uint32 `protobuf:"varint,9,opt,name=tcpi_rto,json=tcpiRto,proto3" json:"tcpi_rto,omitempty"`
	TcpiAto          uint32 `protobuf:"varint,10,opt,name=tcpi_ato,json=tcpiAto,proto3" json:"tcpi_ato,omitempty"`
	TcpiSndMss       uint32 `protobuf:"varint,11,opt,name=tcpi_snd_mss,json=tcpiSndMss,proto3" json:"tcpi_snd_mss,omitempty"`
	TcpiRcvMss       uint32 `protobuf:"varint,12,opt,name=tcpi_rcv_mss,json=tcpiRcvMss,proto3" json:"tcpi_rcv_mss,omitempty"`
	TcpiUnacked      uint32 `protobuf:"varint,13,opt,name=tcpi_unacked,json=tcpiUnacked,proto3" json:"tcpi_unacked,omitempty"`
	TcpiSacked       uint32 `protobuf:"varint,14,opt,name=tcpi_sacked,json=tcpiSacked,proto3" json:"tcpi_sacked,omitempty"`
	TcpiLost         uint32 `protobuf:"varint,15,opt,name=tcpi_lost,json=tcpiLost,proto3" json:"tcpi_lost,omitempty"`
	TcpiRetrans      uint32 `protobuf:"varint,16,opt,name=tcpi_retrans,json=tcpiRetrans,proto3" json:"tcpi_retrans,omitempty"`
	TcpiFackets      uint32 `protobuf:"varint,17,opt,name=tcpi_fackets,json=tcpiFackets,proto3" json:"tcpi_fackets,omitempty"`
	TcpiLastDataSent uint32 `protobuf:"varint,18,opt,name=tcpi_last_data_sent,json=tcpiLastDataSent,proto3" json:"tcpi_last_data_sent,omitempty"`
	TcpiLastAckSent  uint32 `protobuf:"varint,19,opt,name=tcpi_last_ack_sent,json=tcpiLastAckSent,proto3" json:"tcpi_last_ack_sent,omitempty"`
	TcpiLastDataRecv uint32 `protobuf:"varint,20,opt,name=tcpi_last_data_recv,json=tcpiLastDataRecv,proto3" json:"tcpi_last_data_recv,omitempty"`
	TcpiLastAckRecv  uint32 `protobuf:"varint,21,opt,name=tcpi_last_ack_recv,json=tcpiLastAckRecv,proto3" json:"tcpi_last_ack_recv,omitempty"`
	TcpiPmtu         uint32 `protobuf:"varint,22,opt,name=tcpi_pmtu,json=tcpiPmtu,proto3" json:"tcpi_pmtu,omitempty"`
	TcpiRcvSsthresh  uint32 `protobuf:"varint,23,opt,name=tcpi_rcv_ssthresh,json=tcpiRcvSsthresh,proto3" json:"tcpi_rcv_ssthresh,omitempty"`
	TcpiRtt          uint32 `protobuf:"varint,24,opt,name=tcpi_rtt,json=tcpiRtt,proto3" json:"tcpi_rtt,omitempty"`
	TcpiRttvar       uint32 `protobuf:"varint,25,opt,name=tcpi_rttvar,json=tcpiRttvar,proto3" json:"tcpi_rttvar,omitempty"`
	TcpiSndSsthresh  uint32 `protobuf:"varint,26,opt,name=tcpi_snd_ssthresh,json=tcpiSndSsthresh,proto3" json:"tcpi_snd_ssthresh,omitempty"`
	TcpiSndCwnd      uint32 `protobuf:"varint,27,opt,name=tcpi_snd_cwnd,json=tcpiSndCwnd,proto3" json:"tcpi_snd_cwnd,omitempty"`
	TcpiAdvmss       uint32 `protobuf:"varint,28,opt,name=tcpi_advmss,json=tcpiAdvmss,proto3" json:"tcpi_advmss,omitempty"`
	TcpiReordering   uint32 `protobuf:"varint,29,opt,name=tcpi_reordering,json=tcpiReordering,proto3" json:"tcpi_reordering,omitempty"`
}

func (x *SocketOptionTcpInfo) Reset() {
	*x = SocketOptionTcpInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocketOptionTcpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocketOptionTcpInfo) ProtoMessage() {}

func (x *SocketOptionTcpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocketOptionTcpInfo.ProtoReflect.Descriptor instead.
func (*SocketOptionTcpInfo) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{19}
}

func (x *SocketOptionTcpInfo) GetTcpiState() uint32 {
	if x != nil {
		return x.TcpiState
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiCaState() uint32 {
	if x != nil {
		return x.TcpiCaState
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRetransmits() uint32 {
	if x != nil {
		return x.TcpiRetransmits
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiProbes() uint32 {
	if x != nil {
		return x.TcpiProbes
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiBackoff() uint32 {
	if x != nil {
		return x.TcpiBackoff
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiOptions() uint32 {
	if x != nil {
		return x.TcpiOptions
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiSndWscale() uint32 {
	if x != nil {
		return x.TcpiSndWscale
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRcvWscale() uint32 {
	if x != nil {
		return x.TcpiRcvWscale
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRto() uint32 {
	if x != nil {
		return x.TcpiRto
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiAto() uint32 {
	if x != nil {
		return x.TcpiAto
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiSndMss() uint32 {
	if x != nil {
		return x.TcpiSndMss
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRcvMss() uint32 {
	if x != nil {
		return x.TcpiRcvMss
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiUnacked() uint32 {
	if x != nil {
		return x.TcpiUnacked
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiSacked() uint32 {
	if x != nil {
		return x.TcpiSacked
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiLost() uint32 {
	if x != nil {
		return x.TcpiLost
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRetrans() uint32 {
	if x != nil {
		return x.TcpiRetrans
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiFackets() uint32 {
	if x != nil {
		return x.TcpiFackets
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiLastDataSent() uint32 {
	if x != nil {
		return x.TcpiLastDataSent
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiLastAckSent() uint32 {
	if x != nil {
		return x.TcpiLastAckSent
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiLastDataRecv() uint32 {
	if x != nil {
		return x.TcpiLastDataRecv
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiLastAckRecv() uint32 {
	if x != nil {
		return x.TcpiLastAckRecv
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiPmtu() uint32 {
	if x != nil {
		return x.TcpiPmtu
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRcvSsthresh() uint32 {
	if x != nil {
		return x.TcpiRcvSsthresh
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRtt() uint32 {
	if x != nil {
		return x.TcpiRtt
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiRttvar() uint32 {
	if x != nil {
		return x.TcpiRttvar
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiSndSsthresh() uint32 {
	if x != nil {
		return x.TcpiSndSsthresh
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiSndCwnd() uint32 {
	if x != nil {
		return x.TcpiSndCwnd
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiAdvmss() uint32 {
	if x != nil {
		return x.TcpiAdvmss
	}
	return 0
}

func (x *SocketOptionTcpInfo) GetTcpiReordering() uint32 {
	if x != nil {
		return x.TcpiReordering
	}
	return 0
}

type GetTopChannelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start_channel_id indicates that only channels at or above this id should be
	// included in the results.
	// To request the first page, this should be set to 0. To request
	// subsequent pages, the client generates this value by adding 1 to
	// the highest seen result ID.
	StartChannelId int64 `protobuf:"varint,1,opt,name=start_channel_id,json=startChannelId,proto3" json:"start_channel_id,omitempty"`
	// If non-zero, the server will return a page of results containing
	// at most this many items. If zero, the server will choose a
	// reasonable page size.  Must never be negative.
	MaxResults int64 `protobuf:"varint,2,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *GetTopChannelsRequest) Reset() {
	*x = GetTopChannelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopChannelsRequest) ProtoMessage() {}

func (x *GetTopChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetTopChannelsRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{20}
}

func (x *GetTopChannelsRequest) GetStartChannelId() int64 {
	if x != nil {
		return x.StartChannelId
	}
	return 0
}

func (x *GetTopChannelsRequest) GetMaxResults() int64 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

type GetTopChannelsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of channels that the connection detail service knows about.  Sorted in
	// ascending channel_id order.
	// Must contain at least 1 result, otherwise 'end' must be true.
	Channel []*Channel `protobuf:"bytes,1,rep,name=channel,proto3" json:"channel,omitempty"`
	// If set, indicates that the list of channels is the final list.  Requesting
	// more channels can only return more if they are created after this RPC
	// completes.
	End bool `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *GetTopChannelsResponse) Reset() {
	*x = GetTopChannelsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopChannelsResponse) ProtoMessage() {}

func (x *GetTopChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopChannelsResponse.ProtoReflect.Descriptor instead.
func (*GetTopChannelsResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{21}
}

func (x *GetTopChannelsResponse) GetChannel() []*Channel {
	if x != nil {
		return x.Channel
	}
	return nil
}

func (x *GetTopChannelsResponse) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

type GetServersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start_server_id indicates that only servers at or above this id should be
	// included in the results.
	// To request the first page, this must be set to 0. To request
	// subsequent pages, the client generates this value by adding 1 to
	// the highest seen result ID.
	StartServerId int64 `protobuf:"varint,1,opt,name=start_server_id,json=startServerId,proto3" json:"start_server_id,omitempty"`
	// If non-zero, the server will return a page of results containing
	// at most this many items. If zero, the server will choose a
	// reasonable page size.  Must never be negative.
	MaxResults int64 `protobuf:"varint,2,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *GetServersRequest) Reset() {
	*x = GetServersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServersRequest) ProtoMessage() {}

func (x *GetServersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServersRequest.ProtoReflect.Descriptor instead.
func (*GetServersRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{22}
}

func (x *GetServersRequest) GetStartServerId() int64 {
	if x != nil {
		return x.StartServerId
	}
	return 0
}

func (x *GetServersRequest) GetMaxResults() int64 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

type GetServersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of servers that the connection detail service knows about.  Sorted in
	// ascending server_id order.
	// Must contain at least 1 result, otherwise 'end' must be true.
	Server []*Server `protobuf:"bytes,1,rep,name=server,proto3" json:"server,omitempty"`
	// If set, indicates that the list of servers is the final list.  Requesting
	// more servers will only return more if they are created after this RPC
	// completes.
	End bool `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *GetServersResponse) Reset() {
	*x = GetServersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServersResponse) ProtoMessage() {}

func (x *GetServersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServersResponse.ProtoReflect.Descriptor instead.
func (*GetServersResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{23}
}

func (x *GetServersResponse) GetServer() []*Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *GetServersResponse) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

type GetServerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// server_id is the identifier of the specific server to get.
	ServerId int64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *GetServerRequest) Reset() {
	*x = GetServerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerRequest) ProtoMessage() {}

func (x *GetServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerRequest.ProtoReflect.Descriptor instead.
func (*GetServerRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{24}
}

func (x *GetServerRequest) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type GetServerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Server that corresponds to the requested server_id.  This field
	// should be set.
	Server *Server `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
}

func (x *GetServerResponse) Reset() {
	*x = GetServerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerResponse) ProtoMessage() {}

func (x *GetServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerResponse.ProtoReflect.Descriptor instead.
func (*GetServerResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{25}
}

func (x *GetServerResponse) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

type GetServerSocketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId int64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	// start_socket_id indicates that only sockets at or above this id should be
	// included in the results.
	// To request the first page, this must be set to 0. To request
	// subsequent pages, the client generates this value by adding 1 to
	// the highest seen result ID.
	StartSocketId int64 `protobuf:"varint,2,opt,name=start_socket_id,json=startSocketId,proto3" json:"start_socket_id,omitempty"`
	// If non-zero, the server will return a page of results containing
	// at most this many items. If zero, the server will choose a
	// reasonable page size.  Must never be negative.
	MaxResults int64 `protobuf:"varint,3,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *GetServerSocketsRequest) Reset() {
	*x = GetServerSocketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServerSocketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerSocketsRequest) ProtoMessage() {}

func (x *GetServerSocketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerSocketsRequest.ProtoReflect.Descriptor instead.
func (*GetServerSocketsRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{26}
}

func (x *GetServerSocketsRequest) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *GetServerSocketsRequest) GetStartSocketId() int64 {
	if x != nil {
		return x.StartSocketId
	}
	return 0
}

func (x *GetServerSocketsRequest) GetMaxResults() int64 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

type GetServerSocketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of socket refs that the connection detail service knows about.  Sorted in
	// ascending socket_id order.
	// Must contain at least 1 result, otherwise 'end' must be true.
	SocketRef []*SocketRef `protobuf:"bytes,1,rep,name=socket_ref,json=socketRef,proto3" json:"socket_ref,omitempty"`
	// If set, indicates that the list of sockets is the final list.  Requesting
	// more sockets will only return more if they are created after this RPC
	// completes.
	End bool `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *GetServerSocketsResponse) Reset() {
	*x = GetServerSocketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServerSocketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerSocketsResponse) ProtoMessage() {}

func (x *GetServerSocketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerSocketsResponse.ProtoReflect.Descriptor instead.
func (*GetServerSocketsResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{27}
}

func (x *GetServerSocketsResponse) GetSocketRef() []*SocketRef {
	if x != nil {
		return x.SocketRef
	}
	return nil
}

func (x *GetServerSocketsResponse) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

type GetChannelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel_id is the identifier of the specific channel to get.
	ChannelId int64 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
}

func (x *GetChannelRequest) Reset() {
	*x = GetChannelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelRequest) ProtoMessage() {}

func (x *GetChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelRequest.ProtoReflect.Descriptor instead.
func (*GetChannelRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{28}
}

func (x *GetChannelRequest) GetChannelId() int64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

type GetChannelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Channel that corresponds to the requested channel_id.  This field
	// should be set.
	Channel *Channel `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *GetChannelResponse) Reset() {
	*x = GetChannelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelResponse) ProtoMessage() {}

func (x *GetChannelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelResponse.ProtoReflect.Descriptor instead.
func (*GetChannelResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{29}
}

func (x *GetChannelResponse) GetChannel() *Channel {
	if x != nil {
		return x.Channel
	}
	return nil
}

type GetSubchannelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subchannel_id is the identifier of the specific subchannel to get.
	SubchannelId int64 `protobuf:"varint,1,opt,name=subchannel_id,json=subchannelId,proto3" json:"subchannel_id,omitempty"`
}

func (x *GetSubchannelRequest) Reset() {
	*x = GetSubchannelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubchannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubchannelRequest) ProtoMessage() {}

func (x *GetSubchannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubchannelRequest.ProtoReflect.Descriptor instead.
func (*GetSubchannelRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{30}
}

func (x *GetSubchannelRequest) GetSubchannelId() int64 {
	if x != nil {
		return x.SubchannelId
	}
	return 0
}

type GetSubchannelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Subchannel that corresponds to the requested subchannel_id.  This
	// field should be set.
	Subchannel *Subchannel `protobuf:"bytes,1,opt,name=subchannel,proto3" json:"subchannel,omitempty"`
}

func (x *GetSubchannelResponse) Reset() {
	*x = GetSubchannelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubchannelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubchannelResponse) ProtoMessage() {}

func (x *GetSubchannelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubchannelResponse.ProtoReflect.Descriptor instead.
func (*GetSubchannelResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{31}
}

func (x *GetSubchannelResponse) GetSubchannel() *Subchannel {
	if x != nil {
		return x.Subchannel
	}
	return nil
}

type GetSocketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// socket_id is the identifier of the specific socket to get.
	SocketId int64 `protobuf:"varint,1,opt,name=socket_id,json=socketId,proto3" json:"socket_id,omitempty"`
	// If true, the response will contain only high level information
	// that is inexpensive to obtain. Fields that may be omitted are
	// documented.
	Summary bool `protobuf:"varint,2,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *GetSocketRequest) Reset() {
	*x = GetSocketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSocketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocketRequest) ProtoMessage() {}

func (x *GetSocketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocketRequest.ProtoReflect.Descriptor instead.
func (*GetSocketRequest) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{32}
}

func (x *GetSocketRequest) GetSocketId() int64 {
	if x != nil {
		return x.SocketId
	}
	return 0
}

func (x *GetSocketRequest) GetSummary() bool {
	if x != nil {
		return x.Summary
	}
	return false
}

type GetSocketResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Socket that corresponds to the requested socket_id.  This field
	// should be set.
	Socket *Socket `protobuf:"bytes,1,opt,name=socket,proto3" json:"socket,omitempty"`
}

func (x *GetSocketResponse) Reset() {
	*x = GetSocketResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSocketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocketResponse) ProtoMessage() {}

func (x *GetSocketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocketResponse.ProtoReflect.Descriptor instead.
func (*GetSocketResponse) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{33}
}

func (x *GetSocketResponse) GetSocket() *Socket {
	if x != nil {
		return x.Socket
	}
	return nil
}

type Address_TcpIpAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Either the IPv4 or IPv6 address in bytes.  Will be either 4 bytes or 16
	// bytes in length.
	IpAddress []byte `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	// 0-64k, or -1 if not appropriate.
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *Address_TcpIpAddress) Reset() {
	*x = Address_TcpIpAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address_TcpIpAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address_TcpIpAddress) ProtoMessage() {}

func (x *Address_TcpIpAddress) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address_TcpIpAddress.ProtoReflect.Descriptor instead.
func (*Address_TcpIpAddress) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{14, 0}
}

func (x *Address_TcpIpAddress) GetIpAddress() []byte {
	if x != nil {
		return x.IpAddress
	}
	return nil
}

func (x *Address_TcpIpAddress) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

// A Unix Domain Socket address.
type Address_UdsAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
}

func (x *Address_UdsAddress) Reset() {
	*x = Address_UdsAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address_UdsAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address_UdsAddress) ProtoMessage() {}

func (x *Address_UdsAddress) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address_UdsAddress.ProtoReflect.Descriptor instead.
func (*Address_UdsAddress) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{14, 1}
}

func (x *Address_UdsAddress) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

// An address type not included above.
type Address_OtherAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The human readable version of the value.  This value should be set.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The actual address message.
	Value *anypb.Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Address_OtherAddress) Reset() {
	*x = Address_OtherAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address_OtherAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address_OtherAddress) ProtoMessage() {}

func (x *Address_OtherAddress) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address_OtherAddress.ProtoReflect.Descriptor instead.
func (*Address_OtherAddress) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{14, 2}
}

func (x *Address_OtherAddress) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Address_OtherAddress) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

type Security_Tls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to CipherSuite:
	//
	//	*Security_Tls_StandardName
	//	*Security_Tls_OtherName
	CipherSuite isSecurity_Tls_CipherSuite `protobuf_oneof:"cipher_suite"`
	// the certificate used by this endpoint.
	LocalCertificate []byte `protobuf:"bytes,3,opt,name=local_certificate,json=localCertificate,proto3" json:"local_certificate,omitempty"`
	// the certificate used by the remote endpoint.
	RemoteCertificate []byte `protobuf:"bytes,4,opt,name=remote_certificate,json=remoteCertificate,proto3" json:"remote_certificate,omitempty"`
}

func (x *Security_Tls) Reset() {
	*x = Security_Tls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Security_Tls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Security_Tls) ProtoMessage() {}

func (x *Security_Tls) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Security_Tls.ProtoReflect.Descriptor instead.
func (*Security_Tls) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{15, 0}
}

func (m *Security_Tls) GetCipherSuite() isSecurity_Tls_CipherSuite {
	if m != nil {
		return m.CipherSuite
	}
	return nil
}

func (x *Security_Tls) GetStandardName() string {
	if x, ok := x.GetCipherSuite().(*Security_Tls_StandardName); ok {
		return x.StandardName
	}
	return ""
}

func (x *Security_Tls) GetOtherName() string {
	if x, ok := x.GetCipherSuite().(*Security_Tls_OtherName); ok {
		return x.OtherName
	}
	return ""
}

func (x *Security_Tls) GetLocalCertificate() []byte {
	if x != nil {
		return x.LocalCertificate
	}
	return nil
}

func (x *Security_Tls) GetRemoteCertificate() []byte {
	if x != nil {
		return x.RemoteCertificate
	}
	return nil
}

type isSecurity_Tls_CipherSuite interface {
	isSecurity_Tls_CipherSuite()
}

type Security_Tls_StandardName struct {
	// The cipher suite name in the RFC 4346 format:
	// https://tools.ietf.org/html/rfc4346#appendix-C
	StandardName string `protobuf:"bytes,1,opt,name=standard_name,json=standardName,proto3,oneof"`
}

type Security_Tls_OtherName struct {
	// Some other way to describe the cipher suite if
	// the RFC 4346 name is not available.
	OtherName string `protobuf:"bytes,2,opt,name=other_name,json=otherName,proto3,oneof"`
}

func (*Security_Tls_StandardName) isSecurity_Tls_CipherSuite() {}

func (*Security_Tls_OtherName) isSecurity_Tls_CipherSuite() {}

type Security_OtherSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The human readable version of the value.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The actual security details message.
	Value *anypb.Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Security_OtherSecurity) Reset() {
	*x = Security_OtherSecurity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Security_OtherSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Security_OtherSecurity) ProtoMessage() {}

func (x *Security_OtherSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_channelz_v1_channelz_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Security_OtherSecurity.ProtoReflect.Descriptor instead.
func (*Security_OtherSecurity) Descriptor() ([]byte, []int) {
	return file_grpc_channelz_v1_channelz_proto_rawDescGZIP(), []int{15, 1}
}

func (x *Security_OtherSecurity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Security_OtherSecurity) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_grpc_channelz_v1_channelz_proto protoreflect.FileDescriptor

var file_grpc_channelz_v1_channelz_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a,
	0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xaf, 0x02, 0x0a, 0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x03, 0x72,
	0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x31, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3d,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65,
	0x66, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x46, 0x0a,
	0x0e, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63,
	0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x52, 0x09, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x66, 0x22, 0xb5, 0x02, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x31, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x03,
	0x72, 0x65, 0x66, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x0d,
	0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x3a, 0x0a,
	0x0a, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x52, 0x09,
	0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x22, 0xc2, 0x01, 0x0a, 0x18, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x5e,
	0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x09,
	0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x04,
	0x12, 0x0c, 0x0a, 0x08, 0x53, 0x48, 0x55, 0x54, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x05, 0x22, 0xe9,
	0x02, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x34, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x5f, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x61,
	0x6c, 0x6c, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x61, 0x6c, 0x6c, 0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12,
	0x59, 0x0a, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x98, 0x03, 0x0a, 0x11, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69,
	0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65,
	0x66, 0x22, 0x45, 0x0a, 0x08, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x54,
	0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x54,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x68, 0x69, 0x6c,
	0x64, 0x5f, 0x72, 0x65, 0x66, 0x22, 0xc2, 0x01, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x6e, 0x75, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x4c, 0x6f, 0x67, 0x67,
	0x65, 0x64, 0x12, 0x49, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3b, 0x0a,
	0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x63, 0x0a, 0x0a, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x04, 0x08, 0x03, 0x10,
	0x04, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08,
	0x06, 0x10, 0x07, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x4a, 0x04, 0x08, 0x08, 0x10, 0x09, 0x22,
	0x6c, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66,
	0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a,
	0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04, 0x08, 0x04, 0x10,
	0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x22, 0x60, 0x0a,
	0x09, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73,
	0x6f, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x04, 0x08, 0x01, 0x10,
	0x02, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08,
	0x06, 0x10, 0x07, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x4a, 0x04, 0x08, 0x08, 0x10, 0x09, 0x22,
	0x60, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x04, 0x08,
	0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a,
	0x04, 0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x4a, 0x04, 0x08, 0x08, 0x10,
	0x09, 0x22, 0xab, 0x01, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x03,
	0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x66, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x30, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a,
	0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x5f, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x66, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x22,
	0x8e, 0x02, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34,
	0x0a, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x05, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6c,
	0x6c, 0x73, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64,
	0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x59, 0x0a, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x22, 0xa6, 0x02, 0x0a, 0x06, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x03, 0x72,
	0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x66, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x05,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x31, 0x0a,
	0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x12, 0x36, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x83, 0x07, 0x0a, 0x0a, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x5f, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x53, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x65, 0x65, 0x70, 0x5f,
	0x61, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x6b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x53, 0x65, 0x6e,
	0x74, 0x12, 0x68, 0x0a, 0x23, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1f, 0x6c, 0x61, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x6a, 0x0a, 0x24, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x20, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x59, 0x0a, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x61, 0x0a, 0x1f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1c, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x56, 0x0a, 0x19, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x58, 0x0a,
	0x1a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x17,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x36, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x65,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xb8, 0x03, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x74,
	0x63, 0x70, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x54, 0x63,
	0x70, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x63,
	0x70, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x75, 0x64,
	0x73, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x55, 0x64, 0x73, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x75, 0x64, 0x73, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x1a, 0x41, 0x0a, 0x0c, 0x54, 0x63, 0x70, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x28, 0x0a, 0x0a, 0x55, 0x64, 0x73, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x1a,
	0x4e, 0x0a, 0x0c, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x09, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x96, 0x03, 0x0a, 0x08, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x32, 0x0a, 0x03, 0x74, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x2e, 0x54, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x03, 0x74, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x05, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x1a, 0xb9, 0x01,
	0x0a, 0x03, 0x54, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x09, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x43,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x11, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x65,
	0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x63, 0x69, 0x70,
	0x68, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x1a, 0x4f, 0x0a, 0x0d, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x22, 0x6e, 0x0a, 0x0c, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34, 0x0a,
	0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x13, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x63, 0x0a, 0x12, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb2, 0x08, 0x0a, 0x13, 0x53, 0x6f, 0x63, 0x6b, 0x65,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x63, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x74, 0x63, 0x70, 0x69, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a,
	0x0d, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x63, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x43, 0x61, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x74, 0x63, 0x70,
	0x69, 0x52, 0x65, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x63, 0x70, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x6f, 0x66, 0x66, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x42, 0x61, 0x63, 0x6b, 0x6f, 0x66, 0x66,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x73, 0x6e, 0x64, 0x5f,
	0x77, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x74, 0x63,
	0x70, 0x69, 0x53, 0x6e, 0x64, 0x57, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x74,
	0x63, 0x70, 0x69, 0x5f, 0x72, 0x63, 0x76, 0x5f, 0x77, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x74, 0x63, 0x70, 0x69, 0x52, 0x63, 0x76, 0x57, 0x73, 0x63,
	0x61, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x74, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x63, 0x70, 0x69, 0x52, 0x74, 0x6f, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x61, 0x74, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x74, 0x63, 0x70, 0x69, 0x41, 0x74, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x63, 0x70,
	0x69, 0x5f, 0x73, 0x6e, 0x64, 0x5f, 0x6d, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x74, 0x63, 0x70, 0x69, 0x53, 0x6e, 0x64, 0x4d, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x74,
	0x63, 0x70, 0x69, 0x5f, 0x72, 0x63, 0x76, 0x5f, 0x6d, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x52, 0x63, 0x76, 0x4d, 0x73, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x75, 0x6e, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x55, 0x6e, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x73, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x53, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6c, 0x6f, 0x73, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x63, 0x70, 0x69, 0x4c, 0x6f, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x52, 0x65, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x66, 0x61, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x46, 0x61, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x10, 0x74, 0x63, 0x70, 0x69, 0x4c, 0x61, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0f, 0x74, 0x63, 0x70, 0x69, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x74,
	0x12, 0x2d, 0x0a, 0x13, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x72, 0x65, 0x63, 0x76, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x74,
	0x63, 0x70, 0x69, 0x4c, 0x61, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x63, 0x76, 0x12,
	0x2b, 0x0a, 0x12, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x6b,
	0x5f, 0x72, 0x65, 0x63, 0x76, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x74, 0x63, 0x70,
	0x69, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x76, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x63, 0x70, 0x69, 0x5f, 0x70, 0x6d, 0x74, 0x75, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x74, 0x63, 0x70, 0x69, 0x50, 0x6d, 0x74, 0x75, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x63, 0x70,
	0x69, 0x5f, 0x72, 0x63, 0x76, 0x5f, 0x73, 0x73, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x74, 0x63, 0x70, 0x69, 0x52, 0x63, 0x76, 0x53, 0x73, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x74,
	0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x63, 0x70, 0x69, 0x52, 0x74, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x74, 0x74, 0x76, 0x61, 0x72, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x52, 0x74, 0x74, 0x76, 0x61,
	0x72, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x73, 0x6e, 0x64, 0x5f, 0x73, 0x73,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x74, 0x63,
	0x70, 0x69, 0x53, 0x6e, 0x64, 0x53, 0x73, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x12, 0x22, 0x0a,
	0x0d, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x73, 0x6e, 0x64, 0x5f, 0x63, 0x77, 0x6e, 0x64, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x53, 0x6e, 0x64, 0x43, 0x77, 0x6e,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x61, 0x64, 0x76, 0x6d, 0x73, 0x73,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x63, 0x70, 0x69, 0x41, 0x64, 0x76, 0x6d,
	0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x63, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x63, 0x70,
	0x69, 0x52, 0x65, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x62, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22,
	0x5f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x22, 0x5c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x58,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x2f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30,
	0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x22, 0x7f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6f, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x22, 0x68, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a,
	0x0a, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x52, 0x09,
	0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x32, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x22,
	0x49, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x3b, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x49,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x45, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30,
	0x0a, 0x06, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74,
	0x32, 0x9a, 0x05, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x12, 0x63, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12,
	0x27, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x57, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73,
	0x12, 0x23, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x69, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6f, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x23, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x26, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6f, 0x63, 0x6b, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x6f, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x58, 0x0a,
	0x13, 0x69, 0x6f, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x7a, 0x2e, 0x76, 0x31, 0x42, 0x0d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f,
	0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x7a, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x7a, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_channelz_v1_channelz_proto_rawDescOnce sync.Once
	file_grpc_channelz_v1_channelz_proto_rawDescData = file_grpc_channelz_v1_channelz_proto_rawDesc
)

func file_grpc_channelz_v1_channelz_proto_rawDescGZIP() []byte {
	file_grpc_channelz_v1_channelz_proto_rawDescOnce.Do(func() {
		file_grpc_channelz_v1_channelz_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_channelz_v1_channelz_proto_rawDescData)
	})
	return file_grpc_channelz_v1_channelz_proto_rawDescData
}

var file_grpc_channelz_v1_channelz_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_grpc_channelz_v1_channelz_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_grpc_channelz_v1_channelz_proto_goTypes = []any{
	(ChannelConnectivityState_State)(0), // 0: grpc.channelz.v1.ChannelConnectivityState.State
	(ChannelTraceEvent_Severity)(0),     // 1: grpc.channelz.v1.ChannelTraceEvent.Severity
	(*Channel)(nil),                     // 2: grpc.channelz.v1.Channel
	(*Subchannel)(nil),                  // 3: grpc.channelz.v1.Subchannel
	(*ChannelConnectivityState)(nil),    // 4: grpc.channelz.v1.ChannelConnectivityState
	(*ChannelData)(nil),                 // 5: grpc.channelz.v1.ChannelData
	(*ChannelTraceEvent)(nil),           // 6: grpc.channelz.v1.ChannelTraceEvent
	(*ChannelTrace)(nil),                // 7: grpc.channelz.v1.ChannelTrace
	(*ChannelRef)(nil),                  // 8: grpc.channelz.v1.ChannelRef
	(*SubchannelRef)(nil),               // 9: grpc.channelz.v1.SubchannelRef
	(*SocketRef)(nil),                   // 10: grpc.channelz.v1.SocketRef
	(*ServerRef)(nil),                   // 11: grpc.channelz.v1.ServerRef
	(*Server)(nil),                      // 12: grpc.channelz.v1.Server
	(*ServerData)(nil),                  // 13: grpc.channelz.v1.ServerData
	(*Socket)(nil),                      // 14: grpc.channelz.v1.Socket
	(*SocketData)(nil),                  // 15: grpc.channelz.v1.SocketData
	(*Address)(nil),                     // 16: grpc.channelz.v1.Address
	(*Security)(nil),                    // 17: grpc.channelz.v1.Security
	(*SocketOption)(nil),                // 18: grpc.channelz.v1.SocketOption
	(*SocketOptionTimeout)(nil),         // 19: grpc.channelz.v1.SocketOptionTimeout
	(*SocketOptionLinger)(nil),          // 20: grpc.channelz.v1.SocketOptionLinger
	(*SocketOptionTcpInfo)(nil),         // 21: grpc.channelz.v1.SocketOptionTcpInfo
	(*GetTopChannelsRequest)(nil),       // 22: grpc.channelz.v1.GetTopChannelsRequest
	(*GetTopChannelsResponse)(nil),      // 23: grpc.channelz.v1.GetTopChannelsResponse
	(*GetServersRequest)(nil),           // 24: grpc.channelz.v1.GetServersRequest
	(*GetServersResponse)(nil),          // 25: grpc.channelz.v1.GetServersResponse
	(*GetServerRequest)(nil),            // 26: grpc.channelz.v1.GetServerRequest
	(*GetServerResponse)(nil),           // 27: grpc.channelz.v1.GetServerResponse
	(*GetServerSocketsRequest)(nil),     // 28: grpc.channelz.v1.GetServerSocketsRequest
	(*GetServerSocketsResponse)(nil),    // 29: grpc.channelz.v1.GetServerSocketsResponse
	(*GetChannelRequest)(nil),           // 30: grpc.channelz.v1.GetChannelRequest
	(*GetChannelResponse)(nil),          // 31: grpc.channelz.v1.GetChannelResponse
	(*GetSubchannelRequest)(nil),        // 32: grpc.channelz.v1.GetSubchannelRequest
	(*GetSubchannelResponse)(nil),       // 33: grpc.channelz.v1.GetSubchannelResponse
	(*GetSocketRequest)(nil),            // 34: grpc.channelz.v1.GetSocketRequest
	(*GetSocketResponse)(nil),           // 35: grpc.channelz.v1.GetSocketResponse
	(*Address_TcpIpAddress)(nil),        // 36: grpc.channelz.v1.Address.TcpIpAddress
	(*Address_UdsAddress)(nil),          // 37: grpc.channelz.v1.Address.UdsAddress
	(*Address_OtherAddress)(nil),        // 38: grpc.channelz.v1.Address.OtherAddress
	(*Security_Tls)(nil),                // 39: grpc.channelz.v1.Security.Tls
	(*Security_OtherSecurity)(nil),      // 40: grpc.channelz.v1.Security.OtherSecurity
	(*timestamppb.Timestamp)(nil),       // 41: google.protobuf.Timestamp
	(*wrapperspb.Int64Value)(nil),       // 42: google.protobuf.Int64Value
	(*anypb.Any)(nil),                   // 43: google.protobuf.Any
	(*durationpb.Duration)(nil),         // 44: google.protobuf.Duration
}
var file_grpc_channelz_v1_channelz_proto_depIdxs = []int32{
	8,  // 0: grpc.channelz.v1.Channel.ref:type_name -> grpc.channelz.v1.ChannelRef
	5,  // 1: grpc.channelz.v1.Channel.data:type_name -> grpc.channelz.v1.ChannelData
	8,  // 2: grpc.channelz.v1.Channel.channel_ref:type_name -> grpc.channelz.v1.ChannelRef
	9,  // 3: grpc.channelz.v1.Channel.subchannel_ref:type_name -> grpc.channelz.v1.SubchannelRef
	10, // 4: grpc.channelz.v1.Channel.socket_ref:type_name -> grpc.channelz.v1.SocketRef
	9,  // 5: grpc.channelz.v1.Subchannel.ref:type_name -> grpc.channelz.v1.SubchannelRef
	5,  // 6: grpc.channelz.v1.Subchannel.data:type_name -> grpc.channelz.v1.ChannelData
	8,  // 7: grpc.channelz.v1.Subchannel.channel_ref:type_name -> grpc.channelz.v1.ChannelRef
	9,  // 8: grpc.channelz.v1.Subchannel.subchannel_ref:type_name -> grpc.channelz.v1.SubchannelRef
	10, // 9: grpc.channelz.v1.Subchannel.socket_ref:type_name -> grpc.channelz.v1.SocketRef
	0,  // 10: grpc.channelz.v1.ChannelConnectivityState.state:type_name -> grpc.channelz.v1.ChannelConnectivityState.State
	4,  // 11: grpc.channelz.v1.ChannelData.state:type_name -> grpc.channelz.v1.ChannelConnectivityState
	7,  // 12: grpc.channelz.v1.ChannelData.trace:type_name -> grpc.channelz.v1.ChannelTrace
	41, // 13: grpc.channelz.v1.ChannelData.last_call_started_timestamp:type_name -> google.protobuf.Timestamp
	1,  // 14: grpc.channelz.v1.ChannelTraceEvent.severity:type_name -> grpc.channelz.v1.ChannelTraceEvent.Severity
	41, // 15: grpc.channelz.v1.ChannelTraceEvent.timestamp:type_name -> google.protobuf.Timestamp
	8,  // 16: grpc.channelz.v1.ChannelTraceEvent.channel_ref:type_name -> grpc.channelz.v1.ChannelRef
	9,  // 17: grpc.channelz.v1.ChannelTraceEvent.subchannel_ref:type_name -> grpc.channelz.v1.SubchannelRef
	41, // 18: grpc.channelz.v1.ChannelTrace.creation_timestamp:type_name -> google.protobuf.Timestamp
	6,  // 19: grpc.channelz.v1.ChannelTrace.events:type_name -> grpc.channelz.v1.ChannelTraceEvent
	11, // 20: grpc.channelz.v1.Server.ref:type_name -> grpc.channelz.v1.ServerRef
	13, // 21: grpc.channelz.v1.Server.data:type_name -> grpc.channelz.v1.ServerData
	10, // 22: grpc.channelz.v1.Server.listen_socket:type_name -> grpc.channelz.v1.SocketRef
	7,  // 23: grpc.channelz.v1.ServerData.trace:type_name -> grpc.channelz.v1.ChannelTrace
	41, // 24: grpc.channelz.v1.ServerData.last_call_started_timestamp:type_name -> google.protobuf.Timestamp
	10, // 25: grpc.channelz.v1.Socket.ref:type_name -> grpc.channelz.v1.SocketRef
	15, // 26: grpc.channelz.v1.Socket.data:type_name -> grpc.channelz.v1.SocketData
	16, // 27: grpc.channelz.v1.Socket.local:type_name -> grpc.channelz.v1.Address
	16, // 28: grpc.channelz.v1.Socket.remote:type_name -> grpc.channelz.v1.Address
	17, // 29: grpc.channelz.v1.Socket.security:type_name -> grpc.channelz.v1.Security
	41, // 30: grpc.channelz.v1.SocketData.last_local_stream_created_timestamp:type_name -> google.protobuf.Timestamp
	41, // 31: grpc.channelz.v1.SocketData.last_remote_stream_created_timestamp:type_name -> google.protobuf.Timestamp
	41, // 32: grpc.channelz.v1.SocketData.last_message_sent_timestamp:type_name -> google.protobuf.Timestamp
	41, // 33: grpc.channelz.v1.SocketData.last_message_received_timestamp:type_name -> google.protobuf.Timestamp
	42, // 34: grpc.channelz.v1.SocketData.local_flow_control_window:type_name -> google.protobuf.Int64Value
	42, // 35: grpc.channelz.v1.SocketData.remote_flow_control_window:type_name -> google.protobuf.Int64Value
	18, // 36: grpc.channelz.v1.SocketData.option:type_name -> grpc.channelz.v1.SocketOption
	36, // 37: grpc.channelz.v1.Address.tcpip_address:type_name -> grpc.channelz.v1.Address.TcpIpAddress
	37, // 38: grpc.channelz.v1.Address.uds_address:type_name -> grpc.channelz.v1.Address.UdsAddress
	38, // 39: grpc.channelz.v1.Address.other_address:type_name -> grpc.channelz.v1.Address.OtherAddress
	39, // 40: grpc.channelz.v1.Security.tls:type_name -> grpc.channelz.v1.Security.Tls
	40, // 41: grpc.channelz.v1.Security.other:type_name -> grpc.channelz.v1.Security.OtherSecurity
	43, // 42: grpc.channelz.v1.SocketOption.additional:type_name -> google.protobuf.Any
	44, // 43: grpc.channelz.v1.SocketOptionTimeout.duration:type_name -> google.protobuf.Duration
	44, // 44: grpc.channelz.v1.SocketOptionLinger.duration:type_name -> google.protobuf.Duration
	2,  // 45: grpc.channelz.v1.GetTopChannelsResponse.channel:type_name -> grpc.channelz.v1.Channel
	12, // 46: grpc.channelz.v1.GetServersResponse.server:type_name -> grpc.channelz.v1.Server
	12, // 47: grpc.channelz.v1.GetServerResponse.server:type_name -> grpc.channelz.v1.Server
	10, // 48: grpc.channelz.v1.GetServerSocketsResponse.socket_ref:type_name -> grpc.channelz.v1.SocketRef
	2,  // 49: grpc.channelz.v1.GetChannelResponse.channel:type_name -> grpc.channelz.v1.Channel
	3,  // 50: grpc.channelz.v1.GetSubchannelResponse.subchannel:type_name -> grpc.channelz.v1.Subchannel
	14, // 51: grpc.channelz.v1.GetSocketResponse.socket:type_name -> grpc.channelz.v1.Socket
	43, // 52: grpc.channelz.v1.Address.OtherAddress.value:type_name -> google.protobuf.Any
	43, // 53: grpc.channelz.v1.Security.OtherSecurity.value:type_name -> google.protobuf.Any
	22, // 54: grpc.channelz.v1.Channelz.GetTopChannels:input_type -> grpc.channelz.v1.GetTopChannelsRequest
	24, // 55: grpc.channelz.v1.Channelz.GetServers:input_type -> grpc.channelz.v1.GetServersRequest
	26, // 56: grpc.channelz.v1.Channelz.GetServer:input_type -> grpc.channelz.v1.GetServerRequest
	28, // 57: grpc.channelz.v1.Channelz.GetServerSockets:input_type -> grpc.channelz.v1.GetServerSocketsRequest
	30, // 58: grpc.channelz.v1.Channelz.GetChannel:input_type -> grpc.channelz.v1.GetChannelRequest
	32, // 59: grpc.channelz.v1.Channelz.GetSubchannel:input_type -> grpc.channelz.v1.GetSubchannelRequest
	34, // 60: grpc.channelz.v1.Channelz.GetSocket:input_type -> grpc.channelz.v1.GetSocketRequest
	23, // 61: grpc.channelz.v1.Channelz.GetTopChannels:output_type -> grpc.channelz.v1.GetTopChannelsResponse
	25, // 62: grpc.channelz.v1.Channelz.GetServers:output_type -> grpc.channelz.v1.GetServersResponse
	27, // 63: grpc.channelz.v1.Channelz.GetServer:output_type -> grpc.channelz.v1.GetServerResponse
	29, // 64: grpc.channelz.v1.Channelz.GetServerSockets:output_type -> grpc.channelz.v1.GetServerSocketsResponse
	31, // 65: grpc.channelz.v1.Channelz.GetChannel:output_type -> grpc.channelz.v1.GetChannelResponse
	33, // 66: grpc.channelz.v1.Channelz.GetSubchannel:output_type -> grpc.channelz.v1.GetSubchannelResponse
	35, // 67: grpc.channelz.v1.Channelz.GetSocket:output_type -> grpc.channelz.v1.GetSocketResponse
	61, // [61:68] is the sub-list for method output_type
	54, // [54:61] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_grpc_channelz_v1_channelz_proto_init() }
func file_grpc_channelz_v1_channelz_proto_init() {
	if File_grpc_channelz_v1_channelz_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_channelz_v1_channelz_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Channel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Subchannel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ChannelConnectivityState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ChannelData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ChannelTraceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ChannelTrace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ChannelRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SubchannelRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*SocketRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ServerRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ServerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*Socket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SocketData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Security); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*SocketOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*SocketOptionTimeout); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*SocketOptionLinger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*SocketOptionTcpInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GetTopChannelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*GetTopChannelsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*GetServersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*GetServersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*GetServerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*GetServerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*GetServerSocketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*GetServerSocketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*GetChannelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*GetChannelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*GetSubchannelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*GetSubchannelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*GetSocketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*GetSocketResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*Address_TcpIpAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*Address_UdsAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*Address_OtherAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*Security_Tls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_channelz_v1_channelz_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*Security_OtherSecurity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_grpc_channelz_v1_channelz_proto_msgTypes[4].OneofWrappers = []any{
		(*ChannelTraceEvent_ChannelRef)(nil),
		(*ChannelTraceEvent_SubchannelRef)(nil),
	}
	file_grpc_channelz_v1_channelz_proto_msgTypes[14].OneofWrappers = []any{
		(*Address_TcpipAddress)(nil),
		(*Address_UdsAddress_)(nil),
		(*Address_OtherAddress_)(nil),
	}
	file_grpc_channelz_v1_channelz_proto_msgTypes[15].OneofWrappers = []any{
		(*Security_Tls_)(nil),
		(*Security_Other)(nil),
	}
	file_grpc_channelz_v1_channelz_proto_msgTypes[37].OneofWrappers = []any{
		(*Security_Tls_StandardName)(nil),
		(*Security_Tls_OtherName)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_channelz_v1_channelz_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_channelz_v1_channelz_proto_goTypes,
		DependencyIndexes: file_grpc_channelz_v1_channelz_proto_depIdxs,
		EnumInfos:         file_grpc_channelz_v1_channelz_proto_enumTypes,
		MessageInfos:      file_grpc_channelz_v1_channelz_proto_msgTypes,
	}.Build()
	File_grpc_channelz_v1_channelz_proto = out.File
	file_grpc_channelz_v1_channelz_proto_rawDesc = nil
	file_grpc_channelz_v1_channelz_proto_goTypes = nil
	file_grpc_channelz_v1_channelz_proto_depIdxs = nil
}
