/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package channelz

import (
	"syscall"

	"golang.org/x/sys/unix"
)

// SocketOptionData defines the struct to hold socket option data, and related
// getter function to obtain info from fd.
type SocketOptionData struct {
	Linger      *unix.Linger
	RecvTimeout *unix.Timeval
	SendTimeout *unix.Timeval
	TCPInfo     *unix.TCPInfo
}

// Getsockopt defines the function to get socket options requested by channelz.
// It is to be passed to syscall.RawConn.Control().
func (s *SocketOptionData) Getsockopt(fd uintptr) {
	if v, err := unix.GetsockoptLinger(int(fd), syscall.SOL_SOCKET, syscall.SO_LINGER); err == nil {
		s.Linger = v
	}
	if v, err := unix.GetsockoptTimeval(int(fd), syscall.SOL_SOCKET, syscall.SO_RCVTIMEO); err == nil {
		s.RecvTimeout = v
	}
	if v, err := unix.GetsockoptTimeval(int(fd), syscall.SOL_SOCKET, syscall.SO_SNDTIMEO); err == nil {
		s.SendTimeout = v
	}
	if v, err := unix.GetsockoptTCPInfo(int(fd), syscall.SOL_TCP, syscall.TCP_INFO); err == nil {
		s.TCPInfo = v
	}
}

// GetSocketOption gets the socket option info of the conn.
func GetSocketOption(socket any) *SocketOptionData {
	c, ok := socket.(syscall.Conn)
	if !ok {
		return nil
	}
	data := &SocketOptionData{}
	if rawConn, err := c.SyscallConn(); err == nil {
		rawConn.Control(data.Getsockopt)
		return data
	}
	return nil
}
